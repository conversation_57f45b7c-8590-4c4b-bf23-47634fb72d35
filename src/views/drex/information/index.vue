<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">id</label>
        <el-input v-model="query.id" clearable placeholder="主键" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目类型</label>
        <el-select v-model="query.type" clearable size="small" placeholder="项目类型" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_type" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">位置</label>
        <el-select v-model="query.position" clearable size="small" placeholder="位置" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_position" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">DApp名称</label>
        <el-input v-model="query.dappName" clearable placeholder="DApp名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">标题</label>
        <el-input v-model="query.title" clearable placeholder="标题" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目分类</label>
        <el-select v-model="query.category" clearable size="small" placeholder="项目分类" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_category" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">推荐置顶</label>
        <el-select v-model="query.isRecommend" clearable size="small" placeholder="推荐置顶" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.boolean" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="60%"
      >
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
          <el-row :gutter="20"> <!-- 列间距20px -->
            <!-- 左列 -->
            <el-col :span="12">
              <el-form-item label="项目类型" prop="type">
                <el-select v-model="form.type" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.information_type"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="位置" prop="position">
                <el-select v-model="form.position" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.information_position"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="项目分类">
                <el-select
                  v-model="form.category"
                  multiple
                  filterable
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.information_category"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="是否置顶">
                <el-select v-model="form.isRecommend" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.boolean"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="排序值">
                <el-input v-model="form.sort" style="width: 100%" />
              </el-form-item>

              <el-form-item label="标题" prop="title">
                <el-input v-model="form.title" :rows="3" type="textarea" style="width: 100%" />
              </el-form-item>

              <el-form-item label="副标题">
                <el-input v-model="form.subTitle" :rows="3" type="textarea" style="width: 100%" />
              </el-form-item>

              <el-form-item label="活动举办方">
                <el-input v-model="form.organizer" style="width: 100%" />
              </el-form-item>

              <el-form-item label="活动地点">
                <el-input v-model="form.location" style="width: 100%" />
              </el-form-item>

              <el-form-item label="活动开始时间">
                <el-date-picker
                  v-model="form.activityStartTime"
                  type="datetime"
                  placeholder="选择活动开始时间 (UTC+0)"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="utcPickerOptions"
                  @change="handleStartTimeChange"
                />
              </el-form-item>

              <el-form-item label="活动结束时间">
                <el-date-picker
                  v-model="form.activityEndTime"
                  type="datetime"
                  placeholder="选择活动结束时间 (UTC+0)"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="utcPickerOptions"
                  @change="handleEndTimeChange"
                />
              </el-form-item>

            </el-col>

            <!-- 右列 -->
            <el-col :span="12">
              <el-form-item label="DApp名称">
                <el-input v-model="form.dappName" style="width: 100%" />
              </el-form-item>

              <el-form-item label="DApp logo">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="form.dappLogo"
                    placeholder="只能上传jpg/png文件，且不超过5M"
                    style="width: 85%; margin-right: 5px"
                  />
                  <el-upload
                    ref="dappLogoUpload"
                    style="display: inline-block"
                    :limit="1"
                    :before-upload="beforeUpload"
                    :action="informationImageUploadApi"
                    :show-file-list="false"
                    :headers="headers"
                    :data="{id: form.id}"
                    :on-success="handleSuccessDAppLogo"
                    :on-error="handleError"
                  >
                    <el-button size="small" type="success">点击上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>

              <el-form-item label="背景图片">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="form.image"
                    placeholder="只能上传jpg/png文件，且不超过5M"
                    style="width: 85%; margin-right: 5px"
                  />
                  <el-upload
                    ref="imageUpload"
                    style="display: inline-block"
                    :limit="1"
                    :before-upload="beforeUpload"
                    :action="informationImageUploadApi"
                    :show-file-list="false"
                    :headers="headers"
                    :data="{id: form.id}"
                    :on-success="handleSuccessImage"
                    :on-error="handleError"
                  >
                    <el-button size="small" type="success">点击上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>

              <el-form-item label="外部链接">
                <el-input v-model="form.link" style="width: 100%" />
              </el-form-item>

              <el-form-item label="摘要" prop="summary">
                <el-input v-model="form.summary" :rows="3" type="textarea" style="width: 100%" />
              </el-form-item>

              <el-form-item label="内容">
                <el-input v-model="form.content" :rows="3" type="textarea" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="type" label="项目类型">
          <template slot-scope="scope">
            {{ dict.label.information_type[scope.row.type] }}
          </template>
        </el-table-column>
        <el-table-column prop="position" label="位置">
          <template slot-scope="scope">
            {{ dict.label.information_position[scope.row.position] }}
          </template>
        </el-table-column>
        <el-table-column prop="dappName" label="DApp名称" />
        <!--        <el-table-column prop="dappLogo" label="DApp logo" />-->
        <el-table-column prop="title" label="标题" />
        <!--        <el-table-column prop="subTitle" label="副标题" />-->
        <!--        <el-table-column prop="summary" label="摘要" />-->
        <!--        <el-table-column prop="content" label="内容" />-->
        <!--        <el-table-column prop="image" label="图片链接" />-->
        <!--        <el-table-column prop="link" label="外部链接" />-->
        <el-table-column prop="category" label="项目分类">
          <template slot-scope="scope">
            {{ formatCategory(scope.row.category) }}
          </template>
        </el-table-column>
        <el-table-column prop="isRecommend" label="推荐置顶">
          <template slot-scope="scope">
            {{ dict.label.boolean[scope.row.isRecommend] }}
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序值" />
        <!--        <el-table-column prop="organizer" label="活动举办方" />-->
        <!--        <el-table-column prop="location" label="活动地点" />-->
        <!--        <el-table-column prop="activityStartTime" label="活动开始时间" :formatter="formatTimestamp" />-->
        <!--        <el-table-column prop="activityEndTime" label="活动结束时间" :formatter="formatTimestamp" />-->
        <el-table-column prop="created" label="创建时间" :formatter="formatTime" />
        <el-table-column prop="modified" label="修改时间" :formatter="formatTime" />
        <!--        <el-table-column label="数据操作" align="center" width="270">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-button type="primary" size="mini" :disabled="scope.row.status === 1" @click="publishInformation(scope.row)">发布</el-button>-->
        <!--            <el-button type="danger" size="mini" :disabled="scope.row.status === 0" @click="withdrawInformation(scope.row)">撤回</el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudInformation, { publish, withdraw } from '@/api/drex/information/information'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'

const defaultForm = { id: null, type: null, position: null, dappName: null, dappLogo: null, title: null, subTitle: null, summary: null, content: null, image: null, link: null, category: null, isRecommend: null, sort: null, organizer: null, location: null, activityStartTime: null, activityEndTime: null, created: null, modified: null }
export default {
  name: 'Information',
  components: { pagination, crudOperation, rrOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['information_type', 'information_category', 'boolean', 'information_position'],
  cruds() {
    return CRUD({ title: '项目信息配置', url: 'api/information', idField: 'id', sort: 'id,desc', crudMethod: { ...crudInformation }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'information:add'],
        edit: ['admin', 'information:edit'],
        del: ['admin', 'information:del']
      },
      headers: {
        'Authorization': getToken()
      },
      rules: {
        type: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ],
        position: [
          { required: true, message: '位置不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        summary: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'id', display_name: 'id' },
        { key: 'type', display_name: '项目类型' },
        { key: 'position', display_name: '位置' },
        { key: 'dappName', display_name: 'DApp名称' },
        { key: 'title', display_name: '标题' },
        { key: 'category', display_name: '项目分类' },
        { key: 'isRecommend', display_name: '推荐置顶' },
        { key: 'organizer', display_name: '活动举办方' },
        { key: 'location', display_name: '活动地点' }
      ],
      // UTC时区选择器配置
      utcPickerOptions: {
        // 禁用时区转换，强制使用UTC时间
        disabledDate: null,
        // 自定义时间选择器行为
        selectableRange: '00:00:00 - 23:59:59'
      }
    }
  },
  computed: {
    ...mapGetters([
      'informationImageUploadApi'
    ])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 钩子：编辑前处理数据
    [CRUD.HOOK.beforeToEdit](crud, form) {
      // 处理 category 字段，将字符串转换为数组
      if (form.category && typeof form.category === 'string') {
        try {
          form.category = JSON.parse(form.category)
        } catch (e) {
          console.error('解析 category 字段失败:', e)
          form.category = []
        }
      }
      // 处理时间戳字段，将UTC时间戳转换为显示格式
      if (form.activityStartTime) {
        // 确保时间戳是数字类型
        let timestamp = form.activityStartTime
        if (typeof timestamp === 'string') {
          timestamp = parseInt(timestamp)
        }
        // 只有当时间戳是有效数字时才进行格式化
        if (!isNaN(timestamp) && timestamp > 0) {
          form.activityStartTime = this.formatUtcTimestamp(timestamp)
        } else {
          form.activityStartTime = null
        }
      }
      if (form.activityEndTime) {
        // 确保时间戳是数字类型
        let timestamp = form.activityEndTime
        if (typeof timestamp === 'string') {
          timestamp = parseInt(timestamp)
        }
        // 只有当时间戳是有效数字时才进行格式化
        if (!isNaN(timestamp) && timestamp > 0) {
          form.activityEndTime = this.formatUtcTimestamp(timestamp)
        } else {
          form.activityEndTime = null
        }
      }
      return form
    },
    // 钩子：添加/编辑前处理数据
    [CRUD.HOOK.beforeToCU](crud, form) {
      // 处理 category 字段，确保它是数组
      if (form.category && typeof form.category === 'string') {
        try {
          form.category = JSON.parse(form.category)
        } catch (e) {
          console.error('解析 category 字段失败:', e)
          form.category = []
        }
      }
      // 处理时间戳字段，将显示格式转换为UTC时间戳
      if (form.activityStartTime) {
        // 如果不是数字类型，则需要转换
        if (typeof form.activityStartTime !== 'number') {
          form.activityStartTime = this.parseAsUtcTimestamp(form.activityStartTime)
        }
      }
      if (form.activityEndTime) {
        // 如果不是数字类型，则需要转换
        if (typeof form.activityEndTime !== 'number') {
          form.activityEndTime = this.parseAsUtcTimestamp(form.activityEndTime)
        }
      }
      return form
    },
    // 钩子：表单提交前处理数据
    [CRUD.HOOK.beforeSubmit](crud) {
      // 如果 category 是数组，则不需要转换，因为服务端会处理
      // 这里仅做日志记录，以便调试
      if (crud.form.category && Array.isArray(crud.form.category)) {
        console.log('提交的 category 数据:', crud.form.category)
      }
      return true
    },
    publishInformation(row) {
      this.$confirm(`确定发布项目信息 ${row.type} ${row.title} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publish(row.id).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    withdrawInformation(row) {
      this.$confirm(`确定撤回项目信息 ${row.type} ${row.title} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        withdraw(row.id).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    formatTime(row, column, cellValue) {
      if (!cellValue) return '-' // 处理空值
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 24小时制
      }).replace(/\//g, '-') // 将 / 替换为 -
    },
    formatTimestamp(row, column, cellValue) {
      if (!cellValue) return '-' // 处理空值
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 24小时制
      }).replace(/\//g, '-') // 将 / 替换为 -
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 5MB!')
      }
      this.form.name = file.name
      return isLt2M
    },
    handleSuccessDAppLogo(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.dappLogo = response
      // 重置上传组件，以便下次上传
      this.$refs.dappLogoUpload.clearFiles()
    },
    handleSuccessImage(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.image = response
      // 重置上传组件，以便下次上传
      this.$refs.imageUpload.clearFiles()
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    // 格式化项目分类字段
    formatCategory(category) {
      if (!category) return '-'

      // 如果是字符串形式的数组，尝试解析
      if (typeof category === 'string') {
        try {
          const parsedCategory = JSON.parse(category)
          if (Array.isArray(parsedCategory)) {
            return parsedCategory.join(', ')
          }
          return category
        } catch (e) {
          return category
        }
      }

      // 如果已经是数组，直接连接
      if (Array.isArray(category)) {
        return category.join(', ')
      }

      return String(category)
    },
    // 处理活动开始时间变化 - 将选择的时间视为UTC时间
    handleStartTimeChange(value) {
      if (value) {
        // 如果已经是时间戳，直接使用；否则解析为UTC时间戳
        if (typeof value === 'number') {
          this.form.activityStartTime = value
        } else {
          const utcTimestamp = this.parseAsUtcTimestamp(value)
          this.form.activityStartTime = utcTimestamp
        }
      } else {
        this.form.activityStartTime = null
      }
    },
    // 处理活动结束时间变化 - 将选择的时间视为UTC时间
    handleEndTimeChange(value) {
      if (value) {
        // 如果已经是时间戳，直接使用；否则解析为UTC时间戳
        if (typeof value === 'number') {
          this.form.activityEndTime = value
        } else {
          const utcTimestamp = this.parseAsUtcTimestamp(value)
          this.form.activityEndTime = utcTimestamp
        }
      } else {
        this.form.activityEndTime = null
      }
    },
    // 将时间字符串解析为UTC时间戳
    parseAsUtcTimestamp(timeString) {
      if (!timeString) return null

      // 如果是数字（时间戳），直接返回
      if (typeof timeString === 'number') {
        return timeString
      }

      // 如果是日期对象，转为字符串
      if (timeString instanceof Date) {
        const year = timeString.getFullYear()
        const month = String(timeString.getMonth() + 1).padStart(2, '0')
        const day = String(timeString.getDate()).padStart(2, '0')
        const hour = String(timeString.getHours()).padStart(2, '0')
        const minute = String(timeString.getMinutes()).padStart(2, '0')
        const second = String(timeString.getSeconds()).padStart(2, '0')
        timeString = `${year}-${month}-${day} ${hour}:${minute}:${second}`
      }

      // 确保是字符串
      if (typeof timeString !== 'string') {
        console.error('无效的日期格式:', timeString, '类型:', typeof timeString)
        return null
      }

      // 解析时间字符串 "YYYY-MM-DD HH:mm:ss"
      const parts = timeString.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/)
      if (!parts) {
        console.error('时间字符串格式不匹配:', timeString)
        return null
      }

      // 创建UTC Date对象
      const utcDate = new Date(Date.UTC(
        parseInt(parts[1]), // year
        parseInt(parts[2]) - 1, // month (0-based)
        parseInt(parts[3]), // day
        parseInt(parts[4]), // hour
        parseInt(parts[5]), // minute
        parseInt(parts[6]) // second
      ))

      return utcDate.getTime()
    },
    // 将UTC时间戳转换为显示字符串
    formatUtcTimestamp(timestamp) {
      if (!timestamp) return ''

      const date = new Date(timestamp)
      const year = date.getUTCFullYear()
      const month = String(date.getUTCMonth() + 1).padStart(2, '0')
      const day = String(date.getUTCDate()).padStart(2, '0')
      const hour = String(date.getUTCHours()).padStart(2, '0')
      const minute = String(date.getUTCMinutes()).padStart(2, '0')
      const second = String(date.getUTCSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }
  }
}
</script>

<style scoped>

</style>
